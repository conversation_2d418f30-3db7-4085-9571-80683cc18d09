{"meta": {"test_sets": [], "test_metrics": [], "learn_metrics": [{"best_value": "Min", "name": "MultiClass"}], "launch_mode": "Train", "parameters": "", "iteration_count": 10, "learn_sets": ["learn"], "name": "experiment"}, "iterations": [{"learn": [1.097821758], "iteration": 0, "passed_time": 0.0003466938877, "remaining_time": 0.003120244989}, {"learn": [1.096252805], "iteration": 1, "passed_time": 0.0006309582283, "remaining_time": 0.002523832913}, {"learn": [1.095088264], "iteration": 2, "passed_time": 0.0009805676991, "remaining_time": 0.002287991298}, {"learn": [1.093682664], "iteration": 3, "passed_time": 0.001246777467, "remaining_time": 0.0018701662}, {"learn": [1.092696803], "iteration": 4, "passed_time": 0.001590365407, "remaining_time": 0.001590365407}, {"learn": [1.0913244], "iteration": 5, "passed_time": 0.00184555407, "remaining_time": 0.00123036938}, {"learn": [1.090144229], "iteration": 6, "passed_time": 0.002116212357, "remaining_time": 0.0009069481528}, {"learn": [1.088593345], "iteration": 7, "passed_time": 0.002465701597, "remaining_time": 0.0006164253993}, {"learn": [1.087573819], "iteration": 8, "passed_time": 0.002730689024, "remaining_time": 0.0003034098916}, {"learn": [1.086487651], "iteration": 9, "passed_time": 0.003060761083, "remaining_time": 0}]}