# BTC Trading Bot PROFESIONAL v4.2 - VERSIÓN MEJORADA ANTI-OVERFITTING
# Sistema completo con ML balanceado, modelos optimizados y SIN trailing stop/profit
# Diseñado para máxima robustez con niveles fijos de TP/SL
#
# MEJORAS v4.2:
# - Umbrales de confianza y consenso más permisivos (15% y 35%)
# - Modelos ML con configuración balanceada (menos restrictiva)
# - Criterios de validación más flexibles para aceptar más modelos
# - Filtros de señales ultra permisivos con múltiples niveles
# - Más features seleccionados (15 en lugar de 10)
# - Validación cruzada menos estricta (5 folds en lugar de 7)

import ccxt
import pandas as pd
import numpy as np
import talib
import time
import datetime
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.ensemble import (RandomForestClassifier, VotingClassifier, GradientBoostingClassifier,
                             ExtraTreesClassifier, StackingClassifier)
from sklearn.linear_model import LogisticRegression, RidgeClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.utils.class_weight import compute_class_weight
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, f1_score
from sklearn.feature_selection import SelectKBest, f_classif, RFE, SelectFromModel
from sklearn.decomposition import PCA
from xgboost import XGBClassifier
import lightgbm as lgb
try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False
    print("⚠️ CatBoost no disponible - instalando modelos alternativos")

import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
import warnings
from scipy import stats
from scipy.optimize import minimize
import joblib
from collections import defaultdict
warnings.filterwarnings('ignore')

# Configuración DEMO ACTIVA - VERSIÓN ULTRA ROBUSTA v4.2 MEJORADA
LOOKBACK = 50  # Incrementado para mejor contexto histórico
SYMBOL = 'BTC/USDT'  # Cambiar aquí para otro activo: 'ETH/USDT', 'SOL/USDT', etc.
INITIAL_CAPITAL = 10000
MAX_RISK_PER_TRADE = 0.015  # 1.5% por trade (más conservador)
MAX_DAILY_LOSS = 0.04  # 4% pérdida máxima diaria (más conservador)
MIN_WIN_RATE = 0.45  # 45% mínimo (más exigente)
MIN_PROFIT_FACTOR = 1.2  # Más exigente
MIN_CONFIDENCE = 0.15  # REDUCIDO de 0.25 a 0.15 (15%) para generar más señales
MAX_POSITIONS = 1  # Solo una posición por vez

# PARÁMETROS AVANZADOS DE ROBUSTEZ
TP_SL_TOLERANCE = 0.0005  # 0.05% tolerancia para TP/SL
TRAILING_STOP_ENABLED = False  # SIN trailing stop - mantener niveles originales
TRAILING_PROFIT_ENABLED = False  # SIN trailing profit - mantener niveles originales
ATR_MULTIPLIER_SL = 2.5  # Multiplicador ATR para stop loss (más conservador)
ATR_MULTIPLIER_TP = 4.0  # Multiplicador ATR para take profit (mejor R:R)
ENSEMBLE_MIN_AGREEMENT = 0.35  # REDUCIDO de 0.45 a 0.35 (35%) para generar más señales
VOLATILITY_FILTER = True  # Filtrar mercados de alta volatilidad
MAX_VOLATILITY_THRESHOLD = 0.035  # 3.5% volatilidad máxima (más conservador)
FEATURE_SELECTION_ENABLED = True  # Selección automática de features

# NUEVOS PARÁMETROS v4.2 - ROBUSTEZ AVANZADA MEJORADA
WALK_FORWARD_ENABLED = True  # Validación Walk-Forward
WALK_FORWARD_WINDOW = 120  # REDUCIDO de 150 a 120 para validación menos estricta
STACKING_ENABLED = True  # Ensemble con Stacking
EARLY_STOPPING_ENABLED = True  # Early stopping para prevenir overfitting
CROSS_VALIDATION_FOLDS = 5  # REDUCIDO de 7 a 5 para validación menos estricta
MODEL_PERSISTENCE_ENABLED = True  # Guardar/cargar modelos entrenados
FEATURE_IMPORTANCE_THRESHOLD = 0.005  # Umbral más bajo para capturar más features importantes
OUTLIER_DETECTION_ENABLED = True  # Detección y filtrado de outliers
ADAPTIVE_THRESHOLDS = True  # Umbrales adaptativos basados en volatilidad del mercado
MODEL_MONITORING_ENABLED = True  # Monitoreo de degradación del modelo
RETRAINING_THRESHOLD = 0.08  # Umbral más estricto para reentrenamiento (8%)
MAX_FEATURES_SELECTED = 15  # AUMENTADO de 10 a 15 para mejor performance
REGULARIZATION_STRENGTH = 0.15  # REDUCIDO de 0.2 a 0.15 para menos restricción
ENSEMBLE_DIVERSITY_THRESHOLD = 0.20  # REDUCIDO de 0.25 a 0.20 para mayor diversidad
HYPERPARAMETER_OPTIMIZATION = True  # Optimización de hiperparámetros
ADVANCED_FEATURE_ENGINEERING = True  # Feature engineering avanzado
ENSEMBLE_WEIGHTING_METHOD = 'performance_based'  # Método de pesos basado en performance

# Crear directorios
for dir_name in ['modelos', 'logs', 'trades', 'reports']:
    os.makedirs(dir_name, exist_ok=True)

class TradingLogger:
    """Sistema de logging mejorado"""
    def __init__(self):
        self.log_file = f"logs/trading_{datetime.datetime.now().strftime('%Y%m%d')}.log"

    def log(self, message, level="INFO"):
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"{timestamp} [{level}] {message}"
        print(formatted_message)

        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(formatted_message + "\n")
        except:
            pass

logger = TradingLogger()

class ModelValidator:
    """Validación temporal robusta y prevención de overfitting"""

    def __init__(self):
        self.validation_scores = {}
        self.feature_importance_history = {}
        self.model_performance_history = defaultdict(list)

    def walk_forward_validation(self, X, y, model, window_size=WALK_FORWARD_WINDOW):
        """Validación Walk-Forward para series temporales"""
        if not WALK_FORWARD_ENABLED or len(X) < window_size * 2:
            return None

        logger.log("🔄 Ejecutando validación Walk-Forward...")

        scores = []
        n_splits = (len(X) - window_size) // (window_size // 4)  # Overlap del 75%

        for i in range(n_splits):
            start_train = i * (window_size // 4)
            end_train = start_train + window_size
            start_test = end_train
            end_test = min(start_test + (window_size // 4), len(X))

            if end_test <= start_test:
                break

            X_train_fold = X[start_train:end_train]
            y_train_fold = y[start_train:end_train]
            X_test_fold = X[start_test:end_test]
            y_test_fold = y[start_test:end_test]

            try:
                # Clonar modelo para evitar contaminación
                model_clone = model.__class__(**model.get_params())
                model_clone.fit(X_train_fold, y_train_fold)
                score = model_clone.score(X_test_fold, y_test_fold)
                scores.append(score)
            except Exception as e:
                logger.log(f"⚠️ Error en fold Walk-Forward: {str(e)}", "WARNING")
                continue

        if scores:
            mean_score = np.mean(scores)
            std_score = np.std(scores)
            logger.log(f"📊 Walk-Forward Score: {mean_score:.3f} ± {std_score:.3f}")
            return {'mean': mean_score, 'std': std_score, 'scores': scores}

        return None

    def temporal_cross_validation(self, X, y, model, n_splits=CROSS_VALIDATION_FOLDS):
        """Validación cruzada temporal específica para series de tiempo"""
        logger.log("⏰ Ejecutando validación cruzada temporal...")

        tscv = TimeSeriesSplit(n_splits=n_splits)
        scores = []

        for train_idx, test_idx in tscv.split(X):
            X_train_fold = X[train_idx]
            y_train_fold = y[train_idx]
            X_test_fold = X[test_idx]
            y_test_fold = y[test_idx]

            try:
                model_clone = model.__class__(**model.get_params())
                model_clone.fit(X_train_fold, y_train_fold)
                score = model_clone.score(X_test_fold, y_test_fold)
                scores.append(score)
            except Exception as e:
                logger.log(f"⚠️ Error en fold temporal: {str(e)}", "WARNING")
                continue

        if scores:
            mean_score = np.mean(scores)
            std_score = np.std(scores)
            logger.log(f"📊 Validación Temporal Score: {mean_score:.3f} ± {std_score:.3f}")
            return {'mean': mean_score, 'std': std_score, 'scores': scores}

        return None

    def detect_overfitting(self, train_score, val_score, threshold=0.25):
        """Detecta overfitting comparando scores de entrenamiento y validación - MEJORADO v4.2"""
        if train_score is None or val_score is None:
            return False

        overfitting_gap = train_score - val_score
        is_overfitting = overfitting_gap > threshold

        if is_overfitting:
            logger.log(f"🚨 OVERFITTING DETECTADO: Gap = {overfitting_gap:.3f}", "WARNING")

        return is_overfitting

    def calculate_model_stability(self, model_name, current_score):
        """Calcula estabilidad del modelo a lo largo del tiempo"""
        self.model_performance_history[model_name].append(current_score)

        if len(self.model_performance_history[model_name]) < 5:
            return 1.0  # Estabilidad perfecta si no hay suficiente historia

        recent_scores = self.model_performance_history[model_name][-5:]
        stability = 1.0 - np.std(recent_scores)  # Menor variabilidad = mayor estabilidad

        return max(0.0, stability)

class AdvancedFeatureSelector:
    """Selección avanzada de features con múltiples métodos"""

    def __init__(self):
        self.selected_features = None
        self.feature_scores = {}
        self.feature_importance = {}

    def remove_outliers(self, X, y, contamination=0.1):
        """Detecta y remueve outliers usando Isolation Forest"""
        if not OUTLIER_DETECTION_ENABLED:
            return X, y

        logger.log("🔍 Detectando outliers...")

        try:
            from sklearn.ensemble import IsolationForest

            iso_forest = IsolationForest(contamination=contamination, random_state=42)
            outlier_labels = iso_forest.fit_predict(X)

            # Mantener solo inliers (etiqueta 1)
            inlier_mask = outlier_labels == 1
            X_clean = X[inlier_mask]
            y_clean = y[inlier_mask]

            outliers_removed = len(X) - len(X_clean)
            logger.log(f"🧹 Outliers removidos: {outliers_removed} ({outliers_removed/len(X)*100:.1f}%)")

            return X_clean, y_clean

        except Exception as e:
            logger.log(f"⚠️ Error removiendo outliers: {str(e)}", "WARNING")
            return X, y

    def correlation_filter(self, df, threshold=0.95):
        """Remueve features altamente correlacionados"""
        logger.log("🔗 Filtrando features correlacionados...")

        # Calcular matriz de correlación
        corr_matrix = df.corr().abs()

        # Encontrar pares altamente correlacionados
        upper_triangle = corr_matrix.where(
            np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        )

        # Encontrar features a remover
        to_drop = [column for column in upper_triangle.columns
                  if any(upper_triangle[column] > threshold)]

        logger.log(f"📉 Features correlacionados removidos: {len(to_drop)}")

        return df.drop(columns=to_drop)

    def select_best_features(self, X, y, feature_names, max_features=MAX_FEATURES_SELECTED):
        """Selección inteligente de features usando múltiples métodos"""
        logger.log("🎯 Seleccionando mejores features...")

        feature_scores = {}

        # Método 1: Univariate Feature Selection
        try:
            selector_univariate = SelectKBest(score_func=f_classif, k=min(max_features*2, X.shape[1]))
            selector_univariate.fit(X, y)

            univariate_scores = selector_univariate.scores_
            for i, score in enumerate(univariate_scores):
                if i < len(feature_names):
                    feature_scores[feature_names[i]] = feature_scores.get(feature_names[i], 0) + score

        except Exception as e:
            logger.log(f"⚠️ Error en selección univariada: {str(e)}", "WARNING")

        # Método 2: Random Forest Feature Importance
        try:
            rf_selector = RandomForestClassifier(n_estimators=50, random_state=42, n_jobs=-1)
            rf_selector.fit(X, y)

            rf_importance = rf_selector.feature_importances_
            for i, importance in enumerate(rf_importance):
                if i < len(feature_names):
                    feature_scores[feature_names[i]] = feature_scores.get(feature_names[i], 0) + importance * 1000

        except Exception as e:
            logger.log(f"⚠️ Error en importancia RF: {str(e)}", "WARNING")

        # Método 3: L1 Regularization (Lasso)
        try:
            from sklearn.feature_selection import SelectFromModel
            from sklearn.linear_model import LassoCV

            lasso = LassoCV(cv=3, random_state=42, max_iter=1000)
            lasso_selector = SelectFromModel(lasso)
            lasso_selector.fit(X, y)

            selected_mask = lasso_selector.get_support()
            for i, selected in enumerate(selected_mask):
                if i < len(feature_names) and selected:
                    feature_scores[feature_names[i]] = feature_scores.get(feature_names[i], 0) + 100

        except Exception as e:
            logger.log(f"⚠️ Error en selección Lasso: {str(e)}", "WARNING")

        # Seleccionar top features
        if feature_scores:
            sorted_features = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)
            selected_features = [feat[0] for feat in sorted_features[:max_features]]

            self.selected_features = selected_features
            self.feature_scores = feature_scores

            logger.log(f"✅ Seleccionados {len(selected_features)} mejores features")
            logger.log(f"🏆 Top 5 features: {selected_features[:5]}")

            return selected_features

        logger.log("⚠️ No se pudieron seleccionar features, usando todos", "WARNING")
        return feature_names[:max_features]

class DataManager:
    """Gestión profesional de datos"""
    def __init__(self):
        self.exchange = None

    def get_live_data(self, symbol=SYMBOL, timeframe='15m', limit=500):
        """Obtiene datos en vivo de Binance"""
        try:
            if not self.exchange:
                self.exchange = ccxt.binance({
                    'enableRateLimit': True,
                    'options': {'defaultType': 'spot'}
                })

            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            logger.log(f"✅ Datos obtenidos: {len(df)} velas de {timeframe}")
            return df

        except Exception as e:
            logger.log(f"❌ Error obteniendo datos: {str(e)}", "ERROR")
            return self.get_mock_data(timeframe, limit)

    def get_mock_data(self, timeframe='15m', limit=500):
        """Datos simulados realistas para testing"""
        np.random.seed(42)
        freq_map = {'5m': '5T', '15m': '15T', '1h': '1H', '2h': '2H', '4h': '4H'}

        timestamps = pd.date_range(end=pd.Timestamp.now(), periods=limit, freq=freq_map[timeframe])

        # Simulación más realista con tendencias y volatilidad variable
        # Ajustar base_price según el activo
        base_prices = {
            'BTC/USDT': 98000,
            'ETH/USDT': 3500,
            'SOL/USDT': 140,
            'BNB/USDT': 600
        }
        base_price = base_prices.get(SYMBOL, 98000)

        drift = 0.0001
        volatility = 0.001

        prices = [base_price]
        for i in range(1, limit):
            change = drift + np.random.normal(0, volatility)
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)

        close = np.array(prices)
        open_prices = np.roll(close, 1)
        open_prices[0] = base_price

        high = np.maximum(close, open_prices) * (1 + np.random.uniform(0, 0.002, limit))
        low = np.minimum(close, open_prices) * (1 - np.random.uniform(0, 0.002, limit))
        volume = np.random.lognormal(10, 0.5, limit) * 1000

        df = pd.DataFrame({
            'open': open_prices,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        }, index=timestamps)

        logger.log(f"✅ Datos simulados generados: {len(df)} velas")
        return df

class FeatureEngineer:
    """Feature Engineering avanzado y optimizado"""

    @staticmethod
    def add_price_features(df):
        """Features básicos de precio mejorados"""
        # Returns múltiples períodos
        df['returns'] = df['close'].pct_change()
        df['returns_2'] = df['close'].pct_change(2)
        df['returns_5'] = df['close'].pct_change(5)
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))

        # Volatility expandida
        df['volatility'] = df['returns'].rolling(20).std()
        df['volatility_5'] = df['returns'].rolling(5).std()
        df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(50).mean()
        df['volatility_rank'] = df['volatility'].rolling(100).rank(pct=True)

        # Price ratios mejorados
        df['hl_ratio'] = (df['high'] - df['low']) / df['close']
        df['co_ratio'] = (df['close'] - df['open']) / df['open']
        df['oc_ratio'] = (df['open'] - df['close'].shift(1)) / df['close'].shift(1)

        # Momentum features
        df['price_momentum_5'] = df['close'] / df['close'].shift(5) - 1
        df['price_momentum_10'] = df['close'] / df['close'].shift(10) - 1
        df['price_acceleration'] = df['returns'] - df['returns'].shift(1)

        return df

    @staticmethod
    def add_technical_indicators(df):
        """Indicadores técnicos avanzados y optimizados"""
        # RSI múltiples períodos
        df['rsi_7'] = talib.RSI(df['close'], timeperiod=7)
        df['rsi_14'] = talib.RSI(df['close'], timeperiod=14)
        df['rsi_21'] = talib.RSI(df['close'], timeperiod=21)
        df['rsi_overbought'] = (df['rsi_14'] > 70).astype(int)
        df['rsi_oversold'] = (df['rsi_14'] < 30).astype(int)
        df['rsi_divergence'] = df['rsi_14'] - df['rsi_14'].shift(5)

        # Moving Averages expandidas
        df['sma_10'] = talib.SMA(df['close'], timeperiod=10)
        df['sma_20'] = talib.SMA(df['close'], timeperiod=20)
        df['sma_50'] = talib.SMA(df['close'], timeperiod=50)
        df['ema_8'] = talib.EMA(df['close'], timeperiod=8)
        df['ema_12'] = talib.EMA(df['close'], timeperiod=12)
        df['ema_26'] = talib.EMA(df['close'], timeperiod=26)

        # Cruces de medias móviles
        df['sma_cross_20_50'] = np.where(df['sma_20'] > df['sma_50'], 1, -1)
        df['ema_cross_12_26'] = np.where(df['ema_12'] > df['ema_26'], 1, -1)
        df['price_above_sma20'] = np.where(df['close'] > df['sma_20'], 1, 0)

        # MACD mejorado
        df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(df['close'])
        df['macd_cross'] = np.where(df['macd'] > df['macd_signal'], 1, -1)
        df['macd_momentum'] = df['macd_hist'] - df['macd_hist'].shift(1)
        df['macd_strength'] = abs(df['macd'] - df['macd_signal'])

        # Bollinger Bands avanzadas
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(df['close'])
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        df['bb_squeeze'] = (df['bb_width'] < df['bb_width'].rolling(20).quantile(0.2)).astype(int)

        # ATR - CRÍTICO PARA STOP LOSS Y TAKE PROFIT
        df['atr_14'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14)
        df['atr_7'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=7)
        df['atr'] = df['atr_14']  # Mantener compatibilidad
        df['atr_ratio'] = df['atr'] / df['close']
        df['atr_trend'] = df['atr'] / df['atr'].rolling(20).mean()

        # Volume indicators avanzados
        df['volume_sma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        df['volume_trend'] = df['volume'].rolling(5).mean() / df['volume'].rolling(20).mean()
        df['obv'] = talib.OBV(df['close'], df['volume'])
        df['obv_trend'] = df['obv'] / df['obv'].rolling(20).mean()

        # Indicadores de momentum avanzados
        df['adx'] = talib.ADX(df['high'], df['low'], df['close'], timeperiod=14)
        df['cci'] = talib.CCI(df['high'], df['low'], df['close'], timeperiod=14)
        df['williams_r'] = talib.WILLR(df['high'], df['low'], df['close'], timeperiod=14)
        df['roc'] = talib.ROC(df['close'], timeperiod=10)
        df['mom'] = talib.MOM(df['close'], timeperiod=10)

        # Stochastic mejorado
        df['stoch_k'], df['stoch_d'] = talib.STOCH(df['high'], df['low'], df['close'])
        df['stoch_rsi_k'], df['stoch_rsi_d'] = talib.STOCHRSI(df['close'])
        df['stoch_cross'] = np.where(df['stoch_k'] > df['stoch_d'], 1, -1)

        # MFI y análisis de flujo de dinero
        df['mfi'] = talib.MFI(df['high'], df['low'], df['close'], df['volume'], timeperiod=14)
        df['mfi_overbought'] = (df['mfi'] > 80).astype(int)
        df['mfi_oversold'] = (df['mfi'] < 20).astype(int)

        return df

    @staticmethod
    def add_pattern_features(df):
        """Patrones de velas japonesas y estructura de mercado avanzados"""
        # Patrones básicos mejorados
        df['is_green'] = (df['close'] > df['open']).astype(int)
        df['body_size'] = abs(df['close'] - df['open']) / df['close']
        df['upper_shadow'] = (df['high'] - np.maximum(df['open'], df['close'])) / df['close']
        df['lower_shadow'] = (np.minimum(df['open'], df['close']) - df['low']) / df['close']
        df['total_range'] = (df['high'] - df['low']) / df['close']

        # Patrones consecutivos expandidos
        df['green_streak'] = df['is_green'].rolling(3).sum()
        df['red_streak'] = (1 - df['is_green']).rolling(3).sum()
        df['consecutive_direction'] = df['is_green'].rolling(5).sum()

        # Patrones de velas japonesas clásicos
        df['doji'] = (df['body_size'] < 0.002).astype(int)
        df['hammer'] = ((df['lower_shadow'] > 2 * df['body_size']) &
                       (df['upper_shadow'] < df['body_size'])).astype(int)
        df['shooting_star'] = ((df['upper_shadow'] > 2 * df['body_size']) &
                              (df['lower_shadow'] < df['body_size'])).astype(int)
        df['spinning_top'] = ((df['upper_shadow'] > df['body_size']) &
                             (df['lower_shadow'] > df['body_size']) &
                             (df['body_size'] < 0.005)).astype(int)

        # Análisis de estructura de mercado
        df['higher_high'] = (df['high'] > df['high'].shift(1)).astype(int)
        df['lower_low'] = (df['low'] < df['low'].shift(1)).astype(int)
        df['inside_bar'] = ((df['high'] < df['high'].shift(1)) &
                           (df['low'] > df['low'].shift(1))).astype(int)
        df['outside_bar'] = ((df['high'] > df['high'].shift(1)) &
                            (df['low'] < df['low'].shift(1))).astype(int)

        return df

    @staticmethod
    def add_market_structure_features(df):
        """Features de estructura de mercado y contexto avanzados"""
        # Niveles de soporte y resistencia mejorados
        df['resistance_level'] = df['high'].rolling(20).max()
        df['support_level'] = df['low'].rolling(20).min()
        df['resistance_level_50'] = df['high'].rolling(50).max()
        df['support_level_50'] = df['low'].rolling(50).min()
        df['distance_to_resistance'] = (df['resistance_level'] - df['close']) / df['close']
        df['distance_to_support'] = (df['close'] - df['support_level']) / df['close']
        df['distance_to_resistance_50'] = (df['resistance_level_50'] - df['close']) / df['close']
        df['distance_to_support_50'] = (df['close'] - df['support_level_50']) / df['close']

        # Análisis de tendencia mejorado
        df['trend_strength'] = (df['close'] - df['close'].shift(20)) / df['close'].shift(20)
        df['trend_strength_50'] = (df['close'] - df['close'].shift(50)) / df['close'].shift(50)
        df['trend_consistency'] = df['returns'].rolling(10).apply(lambda x: (x > 0).sum() / len(x))
        df['trend_acceleration'] = df['trend_strength'] - df['trend_strength'].shift(5)

        # Análisis de tiempo
        df['hour'] = df.index.hour if hasattr(df.index, 'hour') else 0
        df['day_of_week'] = df.index.dayofweek if hasattr(df.index, 'dayofweek') else 0
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
        df['is_market_open'] = ((df['hour'] >= 9) & (df['hour'] <= 16)).astype(int)

        # Features de contexto de mercado avanzados
        df['volume_price_trend'] = df['volume'] * df['returns']
        df['price_volume_correlation'] = df['returns'].rolling(20).corr(df['volume_ratio'])
        df['volume_momentum'] = df['volume'].rolling(5).mean() / df['volume'].rolling(20).mean()

        # Fractales y patrones de precio
        df['fractal_high'] = ((df['high'] > df['high'].shift(1)) &
                             (df['high'] > df['high'].shift(-1)) &
                             (df['high'] > df['high'].shift(2)) &
                             (df['high'] > df['high'].shift(-2))).astype(int)
        df['fractal_low'] = ((df['low'] < df['low'].shift(1)) &
                            (df['low'] < df['low'].shift(-1)) &
                            (df['low'] < df['low'].shift(2)) &
                            (df['low'] < df['low'].shift(-2))).astype(int)

        return df

    @staticmethod
    def add_advanced_features(df):
        """Features avanzados adicionales para mejorar performance"""
        if not ADVANCED_FEATURE_ENGINEERING:
            return df

        logger.log("🚀 Agregando features avanzados adicionales...")

        # Features de momentum avanzado
        df['momentum_divergence'] = df['roc'] - df['roc'].rolling(10).mean()
        df['price_momentum_ratio'] = df['price_momentum_5'] / (df['price_momentum_10'] + 1e-8)

        # Features de volatilidad avanzada
        df['volatility_breakout'] = (df['volatility'] > df['volatility'].rolling(50).quantile(0.8)).astype(int)
        df['volatility_regime'] = pd.qcut(df['volatility'].fillna(0), q=3, labels=[0, 1, 2], duplicates='drop')

        # Features de volumen avanzado
        df['volume_spike'] = (df['volume_ratio'] > 2.0).astype(int)
        df['volume_dryup'] = (df['volume_ratio'] < 0.5).astype(int)
        df['volume_price_divergence'] = np.where(
            (df['returns'] > 0) & (df['volume_ratio'] < 1), -1,
            np.where((df['returns'] < 0) & (df['volume_ratio'] > 1), 1, 0)
        )

        # Features de correlación cruzada
        df['rsi_macd_correlation'] = df['rsi_14'].rolling(20).corr(df['macd'])
        df['price_volume_sync'] = np.where(
            ((df['returns'] > 0) & (df['volume_ratio'] > 1)) |
            ((df['returns'] < 0) & (df['volume_ratio'] < 1)), 1, 0
        )

        # Features de estructura temporal
        df['intraday_range'] = (df['high'] - df['low']) / df['open']
        df['gap_up'] = np.where(df['open'] > df['close'].shift(1) * 1.002, 1, 0)
        df['gap_down'] = np.where(df['open'] < df['close'].shift(1) * 0.998, 1, 0)

        # Features de regímenes de mercado
        df['bull_market'] = (df['sma_20'] > df['sma_50']).astype(int)
        df['bear_market'] = (df['sma_20'] < df['sma_50']).astype(int)
        df['sideways_market'] = ((abs(df['trend_strength']) < 0.02) &
                                (df['adx'] < 25)).astype(int)

        return df

    @staticmethod
    def create_features(df):
        """Pipeline completo de features avanzados"""
        logger.log("🔧 Creando features avanzados...")

        # Aplicar todos los grupos de features
        df = FeatureEngineer.add_price_features(df)
        df = FeatureEngineer.add_technical_indicators(df)
        df = FeatureEngineer.add_pattern_features(df)
        df = FeatureEngineer.add_market_structure_features(df)
        df = FeatureEngineer.add_advanced_features(df)

        # Limpiar NaN e infinitos
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.fillna(method='ffill').fillna(0)

        # Filtrar volatilidad extrema si está habilitado
        if VOLATILITY_FILTER:
            volatility_mask = df['volatility'] <= MAX_VOLATILITY_THRESHOLD
            extreme_vol_count = (~volatility_mask).sum()
            if extreme_vol_count > 0:
                logger.log(f"🔍 Filtradas {extreme_vol_count} velas por volatilidad extrema")

        logger.log(f"✅ Features creados: {len(df.columns)} columnas")
        logger.log(f"📊 Datos finales: {len(df)} filas")
        return df

    @staticmethod
    def select_best_features(df, target_col, max_features=50):
        """Selección automática de mejores features"""
        if not FEATURE_SELECTION_ENABLED:
            return df

        logger.log("🎯 Seleccionando mejores features...")

        # Excluir columnas no-feature
        exclude_cols = ['open', 'high', 'low', 'close', 'volume', 'timestamp',
                       target_col, 'future_return', 'target', 'target_ml']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        if len(feature_cols) <= max_features:
            return df

        # Calcular correlación con el target
        correlations = {}
        for col in feature_cols:
            try:
                corr = abs(df[col].corr(df[target_col]))
                if not np.isnan(corr):
                    correlations[col] = corr
            except:
                pass

        # Seleccionar top features
        top_features = sorted(correlations.items(), key=lambda x: x[1], reverse=True)[:max_features]
        selected_features = [feat[0] for feat in top_features]

        # Mantener columnas esenciales
        essential_cols = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
        final_cols = essential_cols + selected_features + [target_col, 'future_return', 'target', 'target_ml']
        final_cols = [col for col in final_cols if col in df.columns]

        logger.log(f"📈 Seleccionados {len(selected_features)} mejores features de {len(feature_cols)}")

        return df[final_cols]

class RobustSignalGenerator:
    """Generador de señales ultra robusto con ML avanzado y validación temporal"""

    def __init__(self):
        self.models = {}
        self.ensemble = None
        self.stacking_ensemble = None
        self.scaler = RobustScaler()  # Más robusto a outliers
        self.feature_cols = None
        self.class_weights = None
        self.model_weights = {}
        self.validation_scores = {}
        self.validator = ModelValidator()
        self.feature_selector = AdvancedFeatureSelector()
        self.model_performance_tracker = {}
        self.last_training_time = None
        self.training_data_hash = None

    def prepare_data(self, df, lookback=LOOKBACK):
        """Prepara datos para ML con umbrales adaptativos y validación robusta"""
        logger.log("📊 Preparando datos robustos para ML...")

        # Crear target con umbrales adaptativos más inteligentes
        df['future_return'] = df['close'].pct_change(5).shift(-5)  # Horizonte más largo

        if ADAPTIVE_THRESHOLDS:
            # Umbrales adaptativos basados en múltiples factores
            volatility = df['returns'].rolling(30).std().fillna(0.01)  # Ventana más larga

            # Factor de volatilidad del mercado
            market_vol_factor = volatility / volatility.rolling(100).mean()

            # Factor de tendencia
            trend_factor = df['close'].rolling(20).apply(lambda x: 1 if x.iloc[-1] > x.iloc[0] else 0.8)

            # Factor de volumen
            volume_factor = df['volume'].rolling(20).mean() / df['volume'].rolling(100).mean()
            volume_factor = np.clip(volume_factor, 0.5, 2.0)

            # Combinar factores para umbrales dinámicos
            dynamic_multiplier = market_vol_factor * trend_factor * volume_factor
            base_threshold = volatility * 0.6  # Base más conservadora
            dynamic_threshold = base_threshold * dynamic_multiplier

            # Aplicar límites
            min_threshold = 0.0005  # Umbral mínimo más alto
            max_threshold = 0.02    # Umbral máximo para evitar ruido
            upper_threshold = np.clip(dynamic_threshold, min_threshold, max_threshold)
            lower_threshold = -upper_threshold

            logger.log(f"📊 Umbrales adaptativos: {upper_threshold.mean():.4f} ± {upper_threshold.std():.4f}")
        else:
            # Umbrales fijos más conservadores
            upper_threshold = 0.008  # 0.8%
            lower_threshold = -0.008
            logger.log(f"📊 Umbrales fijos: ±{upper_threshold:.3f}")

        # Crear targets con umbrales adaptativos
        df['target'] = np.where(df['future_return'] > upper_threshold, 1,  # LONG
                               np.where(df['future_return'] < lower_threshold, -1, 0))  # SHORT

        # Para ML necesitamos 0, 1, 2
        df['target_ml'] = df['target'] + 1

        # Filtrar correlaciones altas antes de selección de features
        if FEATURE_SELECTION_ENABLED:
            exclude_cols = ['open', 'high', 'low', 'close', 'volume', 'future_return', 'target', 'target_ml', 'timestamp']
            feature_df = df.drop(columns=[col for col in exclude_cols if col in df.columns])

            # Filtrar correlaciones
            feature_df = self.feature_selector.correlation_filter(feature_df, threshold=0.9)

            # Seleccionar mejores features
            feature_names = feature_df.columns.tolist()
            if len(feature_names) > MAX_FEATURES_SELECTED:
                X_temp = feature_df.values
                y_temp = df['target_ml'].values

                # Remover NaN temporalmente para selección
                mask = ~(np.isnan(X_temp).any(axis=1) | np.isnan(y_temp))
                if mask.sum() > 100:  # Suficientes datos
                    selected_features = self.feature_selector.select_best_features(
                        X_temp[mask], y_temp[mask], feature_names, MAX_FEATURES_SELECTED
                    )
                    self.feature_cols = selected_features
                else:
                    self.feature_cols = feature_names[:MAX_FEATURES_SELECTED]
            else:
                self.feature_cols = feature_names
        else:
            # Seleccionar features manualmente
            exclude_cols = ['open', 'high', 'low', 'close', 'volume', 'future_return', 'target', 'target_ml', 'timestamp']
            self.feature_cols = [col for col in df.columns if col not in exclude_cols]

        # Eliminar filas con NaN
        df_clean = df.dropna()

        if len(df_clean) < lookback + 50:  # Más datos requeridos
            logger.log("⚠️ No hay suficientes datos para entrenamiento robusto", "WARNING")
            return None, None

        X = df_clean[self.feature_cols].values
        y = df_clean['target_ml'].values.ravel()

        # Remover outliers si está habilitado
        if OUTLIER_DETECTION_ENABLED:
            X, y = self.feature_selector.remove_outliers(X, y, contamination=0.05)

        # Validar distribución de clases mejorada
        unique, counts = np.unique(y, return_counts=True)
        class_distribution = dict(zip(unique, counts))

        logger.log("📊 Distribución de clases en datos limpios:")
        total_samples = len(y)
        for val, count in zip(unique, counts):
            label = {0: "SHORT", 1: "NEUTRAL", 2: "LONG"}.get(val, "UNKNOWN")
            percentage = count/total_samples*100
            logger.log(f"  • {label}: {count} ({percentage:.1f}%)")

        # Verificar balance de clases más estricto
        min_class_size = max(20, len(y) * 0.08)  # Mínimo 8% o 20 ejemplos
        class_balance_ok = True
        for class_val, count in class_distribution.items():
            if count < min_class_size:
                logger.log(f"⚠️ Clase {class_val} insuficiente ({count} < {min_class_size})", "WARNING")
                class_balance_ok = False

        if not class_balance_ok:
            logger.log("🔄 Aplicando técnicas de balanceo de clases...")
            # Aquí podrías implementar SMOTE u otras técnicas de balanceo

        # Calcular hash de datos para detectar cambios
        import hashlib
        data_str = f"{X.shape}_{y.shape}_{np.sum(X)}_{np.sum(y)}"
        self.training_data_hash = hashlib.md5(data_str.encode()).hexdigest()

        logger.log(f"✅ Datos preparados: {X.shape[0]} muestras, {X.shape[1]} features")
        logger.log(f"🎯 Features seleccionados: {len(self.feature_cols)}")

        return X, y

    def train_models(self, X_train, y_train):
        """Entrena ensemble ultra robusto con validación temporal y prevención de overfitting"""
        logger.log("🤖 Entrenando ensemble ultra robusto v4.0...")

        # Calcular pesos de clases para balance
        classes = np.unique(y_train)
        weights = compute_class_weight('balanced', classes=classes, y=y_train.ravel())
        self.class_weights = dict(zip(classes, weights))

        logger.log(f"⚖️ Pesos de clases calculados: {self.class_weights}")

        # Modelos BALANCEADOS v4.2 - Menos restrictivos para mejor performance
        models = {
            'rf_balanced': RandomForestClassifier(
                n_estimators=50,    # AUMENTADO de 30 a 50
                max_depth=5,        # AUMENTADO de 3 a 5
                min_samples_split=20,  # REDUCIDO de 50 a 20
                min_samples_leaf=10,   # REDUCIDO de 30 a 10
                max_features=0.5,   # AUMENTADO de 0.3 a 0.5
                max_samples=0.8,    # AUMENTADO de 0.6 a 0.8
                random_state=42,
                n_jobs=-1,
                class_weight=self.class_weights
            ),
            'xgb_balanced': XGBClassifier(
                n_estimators=80,    # AUMENTADO de 50 a 80
                max_depth=4,        # AUMENTADO de 3 a 4
                learning_rate=0.05, # AUMENTADO de 0.01 a 0.05
                reg_alpha=0.2,      # REDUCIDO de 0.5 a 0.2
                reg_lambda=0.5,     # REDUCIDO de 1.0 a 0.5
                subsample=0.8,      # AUMENTADO de 0.7 a 0.8
                colsample_bytree=0.8,  # AUMENTADO de 0.6 a 0.8
                min_child_weight=5,    # REDUCIDO de 20 a 5
                gamma=0.5,          # REDUCIDO de 2.0 a 0.5
                random_state=42,
                n_jobs=-1,
                eval_metric='mlogloss',
                verbosity=0
            ),
            'lgb_balanced': lgb.LGBMClassifier(
                n_estimators=80,    # AUMENTADO de 50 a 80
                max_depth=4,        # AUMENTADO de 3 a 4
                learning_rate=0.05, # AUMENTADO de 0.01 a 0.05
                num_leaves=15,      # AUMENTADO de 7 a 15
                subsample=0.8,      # AUMENTADO de 0.5 a 0.8
                colsample_bytree=0.8,  # AUMENTADO de 0.5 a 0.8
                min_child_samples=20,  # REDUCIDO de 50 a 20
                reg_alpha=1.0,      # REDUCIDO de 5.0 a 1.0
                reg_lambda=1.0,     # REDUCIDO de 5.0 a 1.0
                min_gain_to_split=0.1,  # REDUCIDO de 0.5 a 0.1
                random_state=42,
                n_jobs=-1,
                class_weight=self.class_weights,
                verbosity=-1
            ),
            'lr_balanced': LogisticRegression(
                C=0.1,              # AUMENTADO de 0.01 a 0.1
                penalty='l2',
                max_iter=1000,      # AUMENTADO de 500 a 1000
                random_state=42,
                n_jobs=-1,
                class_weight=self.class_weights,
                solver='liblinear'
            ),
            'lr_ridge': LogisticRegression(
                C=0.05,             # AUMENTADO de 0.001 a 0.05
                penalty='l2',
                max_iter=800,       # AUMENTADO de 300 a 800
                random_state=42,
                n_jobs=-1,
                class_weight=self.class_weights,
                solver='liblinear'
            )
        }

        # Agregar CatBoost balanceado si está disponible
        if CATBOOST_AVAILABLE:
            models['catboost_balanced'] = cb.CatBoostClassifier(
                iterations=50,      # AUMENTADO de 10 a 50
                depth=4,            # AUMENTADO de 2 a 4
                learning_rate=0.05, # AUMENTADO de 0.01 a 0.05
                l2_leaf_reg=5,      # REDUCIDO de 20 a 5
                bootstrap_type='Bernoulli',
                subsample=0.8,      # AUMENTADO de 0.5 a 0.8
                colsample_bylevel=0.8,  # AUMENTADO de 0.5 a 0.8
                random_seed=42,
                verbose=False,
                class_weights=list(self.class_weights.values())
            )

        # Agregar MLP balanceado
        models['mlp_balanced'] = MLPClassifier(
            hidden_layer_sizes=(20, 10),  # AUMENTADO: dos capas
            alpha=1.0,          # REDUCIDO de 5.0 a 1.0
            learning_rate_init=0.01,  # AUMENTADO de 0.001 a 0.01
            max_iter=300,       # AUMENTADO de 100 a 300
            early_stopping=True,
            validation_fraction=0.2,  # REDUCIDO de 0.3 a 0.2
            n_iter_no_change=10,  # AUMENTADO de 5 a 10
            random_state=42
        )

        # Agregar modelos adicionales balanceados
        from sklearn.ensemble import ExtraTreesClassifier, GradientBoostingClassifier
        from sklearn.svm import SVC
        from sklearn.naive_bayes import GaussianNB
        from sklearn.tree import DecisionTreeClassifier

        models['extra_trees_balanced'] = ExtraTreesClassifier(
            n_estimators=30,    # AUMENTADO de 10 a 30
            max_depth=4,        # AUMENTADO de 2 a 4
            min_samples_split=20,  # REDUCIDO de 50 a 20
            min_samples_leaf=10,   # REDUCIDO de 30 a 10
            max_features=0.6,   # AUMENTADO de 0.3 a 0.6
            random_state=42,
            n_jobs=-1,
            class_weight=self.class_weights
        )

        models['decision_tree_balanced'] = DecisionTreeClassifier(
            max_depth=6,        # AUMENTADO de 3 a 6
            min_samples_split=20,  # REDUCIDO de 50 a 20
            min_samples_leaf=10,   # REDUCIDO de 30 a 10
            max_features=0.6,   # AUMENTADO de 0.3 a 0.6
            random_state=42,
            class_weight=self.class_weights
        )

        # SVM con mejor configuración
        models['svm_balanced'] = SVC(
            C=1.0,              # AUMENTADO de 0.1 a 1.0
            kernel='rbf',       # Cambio a RBF para mejor performance
            gamma='scale',      # Gamma automático
            probability=True,   # Necesario para predict_proba
            class_weight=self.class_weights,
            random_state=42
        )

        # Naive Bayes (modelo simple pero efectivo)
        models['naive_bayes'] = GaussianNB()

        # Gradient Boosting balanceado
        models['gb_balanced'] = GradientBoostingClassifier(
            n_estimators=50,
            max_depth=4,
            learning_rate=0.1,
            min_samples_split=20,
            min_samples_leaf=10,
            random_state=42
        )

        # Entrenar modelos con validación temporal robusta
        logger.log("🔄 Entrenando modelos con validación temporal...")
        validated_models = {}

        for name, model in models.items():
            try:
                logger.log(f"  • Entrenando {name}...")

                # Entrenar modelo
                model.fit(X_train, y_train)

                # Calcular score de entrenamiento
                train_score = model.score(X_train, y_train)

                # Validación temporal
                val_result = self.validator.temporal_cross_validation(X_train, y_train, model)

                if val_result:
                    val_score = val_result['mean']
                    val_std = val_result['std']

                    # Detectar overfitting con umbral más permisivo v4.2
                    is_overfitting = self.validator.detect_overfitting(train_score, val_score, threshold=0.30)

                    # Calcular estabilidad
                    stability = self.validator.calculate_model_stability(name, val_score)

                    # Criterios MÁS FLEXIBLES v4.2 para capturar modelos útiles
                    if not is_overfitting and val_score > 0.30 and stability > 0.15:
                        validated_models[name] = model
                        self.validation_scores[name] = val_score
                        self.model_performance_tracker[name] = {
                            'train_score': train_score,
                            'val_score': val_score,
                            'val_std': val_std,
                            'stability': stability,
                            'overfitting': is_overfitting
                        }

                        logger.log(f"  ✅ {name}: Train={train_score:.3f}, Val={val_score:.3f}±{val_std:.3f}, Estabilidad={stability:.3f}")
                    else:
                        # Criterios alternativos ULTRA FLEXIBLES para trading v4.2
                        if val_score > 0.25 and stability > 0.1 and (train_score - val_score) < 0.40:
                            validated_models[name] = model
                            self.validation_scores[name] = val_score
                            self.model_performance_tracker[name] = {
                                'train_score': train_score,
                                'val_score': val_score,
                                'val_std': val_std,
                                'stability': stability,
                                'overfitting': is_overfitting
                            }
                            logger.log(f"  ⚠️ {name}: Aceptado con criterios flexibles - Val={val_score:.3f}")
                        else:
                            logger.log(f"  ❌ {name}: Rechazado - Val={val_score:.3f}, Gap={train_score-val_score:.3f}", "WARNING")
                else:
                    # Fallback sin validación temporal con criterios mejorados
                    if train_score > 0.50:
                        validated_models[name] = model
                        self.validation_scores[name] = train_score
                        logger.log(f"  ⚠️ {name}: Solo train score = {train_score:.3f}")

            except Exception as e:
                logger.log(f"  ❌ Error entrenando {name}: {str(e)}", "ERROR")

        self.models = validated_models

        # Crear ensemble solo si tenemos suficientes modelos diversos
        if len(self.models) >= 3:
            # Calcular pesos basados en performance y diversidad
            self._calculate_ensemble_weights()

            # Crear ensemble tradicional
            estimators = [(name, model) for name, model in self.models.items()]
            self.ensemble = VotingClassifier(
                estimators=estimators,
                voting='soft'
            )
            self.ensemble.fit(X_train, y_train)

            # Crear stacking ensemble si está habilitado
            if STACKING_ENABLED and len(self.models) >= 4:
                self._create_stacking_ensemble(X_train, y_train)

            ensemble_score = self.ensemble.score(X_train, y_train)
            logger.log(f"✅ Ensemble robusto creado - Score: {ensemble_score:.3f}")
            logger.log(f"🎯 Modelos validados: {len(self.models)}")

            # Mostrar resumen de performance
            self._log_model_performance_summary()

        else:
            logger.log("❌ No hay suficientes modelos robustos para ensemble", "ERROR")

        # Guardar modelos si está habilitado
        if MODEL_PERSISTENCE_ENABLED:
            self._save_models()

        # Marcar tiempo de entrenamiento
        self.last_training_time = datetime.datetime.now()

    def _calculate_ensemble_weights(self):
        """Calcula pesos inteligentes para el ensemble basados en performance y diversidad"""
        if not self.validation_scores:
            return

        # Normalizar scores de validación
        scores = np.array(list(self.validation_scores.values()))
        normalized_scores = (scores - scores.min()) / (scores.max() - scores.min() + 1e-8)

        # Calcular diversidad entre modelos (simplificado)
        diversity_bonus = 1.0  # Bonus base por diversidad

        # Asignar pesos
        total_score = sum(normalized_scores)
        for i, (name, _) in enumerate(self.models.items()):
            base_weight = normalized_scores[i] / total_score
            self.model_weights[name] = base_weight * diversity_bonus

        # Normalizar pesos finales
        total_weight = sum(self.model_weights.values())
        for name in self.model_weights:
            self.model_weights[name] /= total_weight

        logger.log("📊 Pesos de ensemble calculados:")
        for name, weight in self.model_weights.items():
            logger.log(f"  • {name}: {weight:.3f}")

    def _create_stacking_ensemble(self, X_train, y_train):
        """Crea ensemble de stacking avanzado"""
        try:
            logger.log("🏗️ Creando Stacking Ensemble...")

            # Seleccionar mejores modelos base para stacking
            base_models = list(self.models.items())[:5]  # Top 5 modelos

            # Meta-learner más simple para evitar overfitting
            meta_learner = LogisticRegression(
                C=0.1,  # Más regularización
                random_state=42,
                class_weight=self.class_weights
            )

            self.stacking_ensemble = StackingClassifier(
                estimators=base_models,
                final_estimator=meta_learner,
                cv=3,  # Validación cruzada para meta-features
                n_jobs=-1
            )

            self.stacking_ensemble.fit(X_train, y_train)

            stacking_score = self.stacking_ensemble.score(X_train, y_train)
            logger.log(f"✅ Stacking Ensemble creado - Score: {stacking_score:.3f}")

        except Exception as e:
            logger.log(f"⚠️ Error creando Stacking Ensemble: {str(e)}", "WARNING")
            self.stacking_ensemble = None

    def _log_model_performance_summary(self):
        """Muestra resumen detallado de performance de modelos"""
        logger.log("\n📊 RESUMEN DE PERFORMANCE DE MODELOS:")
        logger.log("="*50)

        for name, metrics in self.model_performance_tracker.items():
            logger.log(f"{name}:")
            logger.log(f"  • Train Score: {metrics.get('train_score', 0):.3f}")
            logger.log(f"  • Val Score: {metrics.get('val_score', 0):.3f} ± {metrics.get('val_std', 0):.3f}")
            logger.log(f"  • Estabilidad: {metrics.get('stability', 0):.3f}")
            logger.log(f"  • Overfitting: {'❌' if metrics.get('overfitting', False) else '✅'}")

        logger.log("="*50)

    def _save_models(self):
        """Guarda modelos entrenados para persistencia"""
        try:
            model_data = {
                'models': self.models,
                'ensemble': self.ensemble,
                'stacking_ensemble': self.stacking_ensemble,
                'scaler': self.scaler,
                'feature_cols': self.feature_cols,
                'class_weights': self.class_weights,
                'model_weights': self.model_weights,
                'validation_scores': self.validation_scores,
                'training_time': self.last_training_time,
                'data_hash': self.training_data_hash
            }

            joblib.dump(model_data, 'modelos/robust_models_v4.pkl')
            logger.log("💾 Modelos guardados exitosamente")

        except Exception as e:
            logger.log(f"⚠️ Error guardando modelos: {str(e)}", "WARNING")

    def _generate_technical_signals(self, df):
        """Genera señales basadas solo en análisis técnico cuando no hay modelos ML"""
        try:
            logger.log("📈 Generando señales con análisis técnico puro...")

            if len(df) < 50:
                logger.log("⚠️ Datos insuficientes para análisis técnico", "WARNING")
                return pd.DataFrame()

            # Obtener últimos valores
            latest = df.iloc[-1]

            # Análisis técnico básico
            rsi = latest.get('rsi', 50)
            macd = latest.get('macd', 0)
            adx = latest.get('adx', 20)
            price = latest.get('close', 0)
            sma_20 = latest.get('sma_20', price)
            sma_50 = latest.get('sma_50', price)

            # Lógica de señales técnicas conservadoras
            signal = 0  # Neutral por defecto
            confidence = 0.0

            # Condiciones para LONG
            if (rsi < 40 and macd > 0 and price > sma_20 and sma_20 > sma_50 and adx > 25):
                signal = 1  # LONG
                confidence = 0.45  # Baja confianza sin ML

            # Condiciones para SHORT
            elif (rsi > 60 and macd < 0 and price < sma_20 and sma_20 < sma_50 and adx > 25):
                signal = -1  # SHORT
                confidence = 0.45  # Baja confianza sin ML

            # Solo generar señal si hay confianza mínima
            if confidence < 0.40:  # Umbral más bajo para técnico
                logger.log("📊 Análisis técnico: Condiciones no favorables")
                logger.log(f"   RSI: {rsi:.1f}, MACD: {macd:.1f}, ADX: {adx:.1f}")
                logger.log("❌ No hay señales técnicas de calidad suficiente")
                return pd.DataFrame()

            # Crear señal técnica
            signals = pd.DataFrame({
                'timestamp': [df.index[-1]],
                'signal': [signal],
                'confidence': [confidence],
                'ensemble_confidence': [confidence],
                'consensus_score': [1.0],  # 100% consenso (solo análisis técnico)
                'price': [price],
                'atr': [latest.get('atr', price * 0.01)],
                'quality_score': [1.0],
                'model_count': [0]  # Sin modelos ML
            })

            signal_type = {-1: "SHORT", 0: "NEUTRAL", 1: "LONG"}[signal]
            logger.log(f"📊 Señal técnica generada: {signal_type}")
            logger.log(f"🎯 Confianza técnica: {confidence:.1%}")
            logger.log(f"⚠️ NOTA: Señal basada solo en análisis técnico (sin ML)")

            return signals

        except Exception as e:
            logger.log(f"❌ Error en análisis técnico: {str(e)}", "ERROR")
            return pd.DataFrame()

    def generate_signals(self, df):
        """Genera señales avanzadas con validación de consenso y calidad"""
        if not self.models or not self.ensemble:
            logger.log("🔍 Modo análisis técnico - Sin modelos ML validados", "INFO")
            logger.log("🛡️ Sistema rechazó todos los modelos por overfitting (CORRECTO)")
            logger.log("📊 Analizando mercado solo con indicadores técnicos...")
            return self._generate_technical_signals(df)

        X, _ = self.prepare_data(df)
        if X is None:
            return pd.DataFrame()

        # Predicciones del ensemble y modelos individuales
        try:
            # Predicciones del ensemble principal
            ensemble_predictions = self.ensemble.predict(X)
            ensemble_probabilities = self.ensemble.predict_proba(X)

            # Predicciones de modelos individuales para consenso
            individual_predictions = {}
            individual_probabilities = {}

            for name, model in self.models.items():
                try:
                    pred = model.predict(X)
                    prob = model.predict_proba(X)

                    # Normalizar formas de predicciones
                    if pred.ndim > 1:
                        pred = pred.flatten()
                    if prob.ndim > 2:
                        prob = prob.reshape(prob.shape[0], -1)

                    individual_predictions[name] = pred
                    individual_probabilities[name] = prob
                except Exception as e:
                    logger.log(f"⚠️ Error en predicción de {name}: {str(e)}", "WARNING")

            # Calcular consenso entre modelos
            if len(individual_predictions) > 1:
                # Convertir predicciones a matriz con verificación mejorada
                try:
                    pred_list = list(individual_predictions.values())
                    # Verificar y normalizar formas
                    normalized_preds = []
                    for pred in pred_list:
                        if pred.ndim > 1:
                            pred = pred.flatten()
                        normalized_preds.append(pred)

                    # Verificar que todas tengan la misma longitud
                    lengths = [len(pred) for pred in normalized_preds]
                    if len(set(lengths)) > 1:
                        logger.log(f"⚠️ Longitudes inconsistentes: {lengths}. Usando ensemble promedio.", "WARNING")
                        # Usar la longitud mínima común
                        min_length = min(lengths)
                        normalized_preds = [pred[:min_length] for pred in normalized_preds]

                    pred_matrix = np.array(normalized_preds)
                    logger.log(f"✅ Ensemble usando {len(normalized_preds)} modelos con forma {pred_matrix.shape}")

                except Exception as e:
                    logger.log(f"⚠️ Error creando matriz de predicciones: {e}. Usando fallback.", "WARNING")
                    # Fallback: usar solo el primer modelo
                    first_pred = list(individual_predictions.values())[0]
                    if first_pred.ndim > 1:
                        first_pred = first_pred.flatten()
                    pred_matrix = first_pred.reshape(1, -1)

                # Calcular acuerdo por posición
                agreement_scores = []
                for i in range(pred_matrix.shape[1]):
                    predictions_at_i = pred_matrix[:, i]
                    # Calcular porcentaje de acuerdo con la predicción más común
                    most_common = stats.mode(predictions_at_i, keepdims=True)[0][0]
                    agreement = np.mean(predictions_at_i == most_common)
                    agreement_scores.append(agreement)

                agreement_scores = np.array(agreement_scores)
            else:
                agreement_scores = np.ones(len(ensemble_predictions))

            # Calcular confianza combinada (ensemble + consenso) con validación NaN
            ensemble_confidence = np.max(ensemble_probabilities, axis=1)

            # Validar y limpiar NaN values
            ensemble_confidence = np.nan_to_num(ensemble_confidence, nan=0.0, posinf=1.0, neginf=0.0)
            agreement_scores = np.nan_to_num(agreement_scores, nan=0.0, posinf=1.0, neginf=0.0)

            combined_confidence = ensemble_confidence * agreement_scores
            combined_confidence = np.nan_to_num(combined_confidence, nan=0.0, posinf=1.0, neginf=0.0)

            # Filtrado ULTRA PERMISIVO v4.2 para generar MÁS señales
            confidence_mask = combined_confidence >= MIN_CONFIDENCE
            consensus_mask = agreement_scores >= ENSEMBLE_MIN_AGREEMENT

            # Criterios alternativos MUY flexibles v4.2
            low_confidence_mask = combined_confidence >= (MIN_CONFIDENCE * 0.6)  # 9% (60% de 15%)
            low_consensus_mask = agreement_scores >= (ENSEMBLE_MIN_AGREEMENT * 0.6)  # 21% (60% de 35%)

            # Criterios mínimos de emergencia v4.2 (aún más permisivos)
            emergency_confidence_mask = combined_confidence >= 0.10  # 10% mínimo absoluto
            emergency_consensus_mask = agreement_scores >= 0.25  # 25% mínimo absoluto

            # Criterios de última oportunidad v4.2
            last_chance_confidence_mask = combined_confidence >= 0.05  # 5% ultra mínimo
            last_chance_consensus_mask = agreement_scores >= 0.15  # 15% ultra mínimo

            # Usar múltiples criterios OR para MÁXIMA permisividad v4.2
            quality_mask = (
                (confidence_mask & consensus_mask) |  # Criterio ideal
                (low_confidence_mask & low_consensus_mask) |  # Criterio relajado
                (emergency_confidence_mask & emergency_consensus_mask) |  # Criterio de emergencia
                (last_chance_confidence_mask & last_chance_consensus_mask)  # Criterio última oportunidad
            )

            # Aplicar filtros más permisivos
            filtered_predictions = np.where(quality_mask, ensemble_predictions, 1)  # 1 = NEUTRAL

            # Mapear predicciones: 0=SHORT, 1=NEUTRAL, 2=LONG
            signal_map = {0: -1, 1: 0, 2: 1}
            mapped_signals = np.array([signal_map.get(int(p), 0) for p in filtered_predictions])

            # Añadir ATR para cálculo de TP/SL
            atr_values = df['atr'].iloc[-len(ensemble_predictions):].values if 'atr' in df.columns else np.full(len(ensemble_predictions), df['close'].mean() * 0.005)

            # Crear DataFrame de señales con precio actual
            current_price = df['close'].iloc[-1]  # Precio más reciente
            current_timestamp = df.index[-1]      # Timestamp más reciente
            current_atr = atr_values[-1] if len(atr_values) > 0 else current_price * 0.005

            # Solo usar la señal más reciente para trading en vivo
            if len(mapped_signals) > 0:
                latest_signal = mapped_signals[-1]
                latest_confidence = combined_confidence[-1]
                latest_ensemble_conf = ensemble_confidence[-1]
                latest_consensus = agreement_scores[-1]

                signals = pd.DataFrame({
                    'timestamp': [current_timestamp],
                    'signal': [latest_signal],
                    'confidence': [latest_confidence],
                    'ensemble_confidence': [latest_ensemble_conf],
                    'consensus_score': [latest_consensus],
                    'price': [current_price],  # Usar precio actual
                    'atr': [current_atr]
                })
            else:
                signals = pd.DataFrame()

            # Solo señales no neutrales
            signals = signals[signals['signal'] != 0]

            # VALIDACIÓN AVANZADA DE DIVERSIDAD DE SEÑALES
            if not signals.empty:
                unique_signals = signals['signal'].value_counts()
                total_signals = len(signals)

                # Verificar si hay demasiada concentración en un tipo de señal
                if len(unique_signals) == 1:
                    dominant_signal = unique_signals.index[0]
                    logger.log(f"⚠️ Señales uniformes detectadas: 100% {['SHORT', 'NEUTRAL', 'LONG'][dominant_signal+1]}", "WARNING")

                    # Aplicar filtros más estrictos para mejorar diversidad
                    if total_signals > 50:  # Solo si hay suficientes señales
                        # Aumentar umbral de confianza para reducir señales
                        higher_confidence = MIN_CONFIDENCE * 1.1
                        filtered_signals = signals[signals['confidence'] >= higher_confidence]

                        if len(filtered_signals) > 0 and len(filtered_signals) < total_signals * 0.8:
                            signals = filtered_signals
                            logger.log(f"🔧 Filtro de diversidad aplicado: confianza mínima aumentada a {higher_confidence:.2%}")
                            logger.log(f"📊 Señales reducidas de {total_signals} a {len(signals)} para mejor calidad")
                        else:
                            logger.log("⚠️ Filtro de diversidad no aplicado - insuficientes señales alternativas")

                elif len(unique_signals) > 1:
                    # Verificar balance entre tipos de señales
                    max_proportion = unique_signals.max() / total_signals
                    if max_proportion > 0.85:  # Si más del 85% son del mismo tipo
                        dominant_type = unique_signals.idxmax()
                        logger.log(f"⚠️ Desbalance de señales: {max_proportion:.1%} son {['SHORT', 'NEUTRAL', 'LONG'][dominant_type+1]}", "WARNING")

            # Log de debug mejorado con métricas avanzadas
            total_predictions = len(ensemble_predictions)
            unique_preds, counts = np.unique(ensemble_predictions, return_counts=True)
            logger.log(f"📊 Total predicciones: {total_predictions}")
            logger.log(f"🎯 Predicciones por tipo:")
            for pred, count in zip(unique_preds, counts):
                pred_type = {0: "SHORT", 1: "NEUTRAL", 2: "LONG"}.get(pred, "UNKNOWN")
                logger.log(f"  • {pred_type}: {count} ({count/total_predictions*100:.1f}%)")

            # Debug detallado del filtrado con validación NaN
            logger.log(f"🔍 DEBUG FILTRADO:")

            # Validar que no hay NaN antes de mostrar
            conf_min = combined_confidence.min() if not np.isnan(combined_confidence.min()) else 0.0
            conf_max = combined_confidence.max() if not np.isnan(combined_confidence.max()) else 0.0
            cons_min = agreement_scores.min() if not np.isnan(agreement_scores.min()) else 0.0
            cons_max = agreement_scores.max() if not np.isnan(agreement_scores.max()) else 0.0

            logger.log(f"  • Confianza combinada rango: {conf_min:.3f} - {conf_max:.3f}")
            logger.log(f"  • Consenso rango: {cons_min:.3f} - {cons_max:.3f}")
            logger.log(f"  • Umbral confianza: {MIN_CONFIDENCE:.3f}")
            logger.log(f"  • Umbral consenso: {ENSEMBLE_MIN_AGREEMENT:.3f}")
            logger.log(f"  • Señales que pasan filtro ideal: {np.sum(confidence_mask & consensus_mask)}")
            logger.log(f"  • Señales que pasan filtro relajado: {np.sum(low_confidence_mask & low_consensus_mask)}")
            logger.log(f"  • Señales que pasan filtro emergencia: {np.sum(emergency_confidence_mask & emergency_consensus_mask)}")
            logger.log(f"  • Señales que pasan filtro última oportunidad: {np.sum(last_chance_confidence_mask & last_chance_consensus_mask)}")
            logger.log(f"  • Total señales que pasan filtros: {np.sum(quality_mask)}")

            # Debug adicional para detectar problemas
            nan_count_conf = np.sum(np.isnan(combined_confidence))
            nan_count_cons = np.sum(np.isnan(agreement_scores))
            if nan_count_conf > 0 or nan_count_cons > 0:
                logger.log(f"  ⚠️ NaN detectados - Confianza: {nan_count_conf}, Consenso: {nan_count_cons}", "WARNING")

            # Mostrar métricas de calidad
            if len(signals) > 0:
                logger.log(f"📈 Confianza combinada: {signals['confidence'].min():.2%} - {signals['confidence'].max():.2%}")
                logger.log(f"🤝 Consenso promedio: {signals['consensus_score'].mean():.2%}")
                logger.log(f"🎯 Ensemble confianza: {signals['ensemble_confidence'].mean():.2%}")

            # Estadísticas de filtrado
            filtered_count = len(signals)
            filter_rate = (total_predictions - filtered_count) / total_predictions * 100
            logger.log(f"🔍 Filtrado: {filter_rate:.1f}% de predicciones eliminadas por baja calidad")

            logger.log(f"\n✅ Señales de alta calidad: {filtered_count}")
            if filtered_count > 0:
                logger.log(f"🔥 ¡SEÑALES ACTIVAS DETECTADAS! Sistema listo para trading.")

            return signals

        except Exception as e:
            logger.log(f"❌ Error generando señales: {str(e)}", "ERROR")
            import traceback
            logger.log(f"📋 Traceback: {traceback.format_exc()}", "ERROR")
            return pd.DataFrame()

class RiskManager:
    """Gestión de riesgo profesional"""

    def __init__(self, initial_capital=INITIAL_CAPITAL):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = []
        self.daily_pnl = 0
        self.trade_history = []

    def calculate_position_size(self, signal_confidence):
        """Calcula tamaño de posición basado en Kelly Criterion modificado"""
        # Kelly conservador
        kelly_fraction = (signal_confidence - 0.5) / 0.5
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Máximo 25% Kelly

        # Ajustar por capital actual
        position_size = self.current_capital * kelly_fraction * MAX_RISK_PER_TRADE

        # Establecer un tamaño mínimo de posición si la señal es válida
        min_position = max(50, self.current_capital * 0.005)  # $50 o 0.5% del capital
        if position_size < min_position and signal_confidence >= MIN_CONFIDENCE:
            position_size = min_position

        # Límites de seguridad
        max_position = self.current_capital * 0.1  # Máximo 10% del capital
        position_size = min(position_size, max_position)

        return position_size

    def calculate_stop_loss(self, entry_price, atr, direction):
        """Stop loss dinámico mejorado basado en ATR"""
        atr_multiplier = ATR_MULTIPLIER_SL  # Usar configuración global

        if direction == 1:  # LONG
            stop_loss = entry_price - (atr * atr_multiplier)
        else:  # SHORT
            stop_loss = entry_price + (atr * atr_multiplier)

        return stop_loss

    def calculate_take_profit(self, entry_price, atr, direction):
        """Take profit optimizado con ratio riesgo/beneficio mejorado"""
        tp_multiplier = ATR_MULTIPLIER_TP  # Usar configuración global
        tp_distance = atr * tp_multiplier

        if direction == 1:  # LONG
            take_profit = entry_price + tp_distance
        else:  # SHORT
            take_profit = entry_price - tp_distance

        return take_profit

    def calculate_trailing_stop(self, entry_price, current_price, direction, atr, best_price=None):
        """Trailing stop loss dinámico"""
        if not TRAILING_STOP_ENABLED:
            return self.calculate_stop_loss(entry_price, atr, direction)

        if best_price is None:
            best_price = current_price

        trailing_distance = atr * (ATR_MULTIPLIER_SL * 0.8)  # Más ajustado que SL inicial

        if direction == 1:  # LONG
            # Solo mover el stop loss hacia arriba
            trailing_stop = best_price - trailing_distance
            initial_stop = self.calculate_stop_loss(entry_price, atr, direction)
            return max(trailing_stop, initial_stop)
        else:  # SHORT
            # Solo mover el stop loss hacia abajo
            trailing_stop = best_price + trailing_distance
            initial_stop = self.calculate_stop_loss(entry_price, atr, direction)
            return min(trailing_stop, initial_stop)

    def is_stop_loss_hit(self, current_price, stop_loss, direction):
        """Detección robusta de stop loss con tolerancia"""
        tolerance = TP_SL_TOLERANCE

        if direction == 1:  # LONG
            # Para LONG, SL se activa si precio <= stop_loss (con tolerancia)
            return current_price <= (stop_loss * (1 + tolerance))
        else:  # SHORT
            # Para SHORT, SL se activa si precio >= stop_loss (con tolerancia)
            return current_price >= (stop_loss * (1 - tolerance))

    def is_take_profit_hit(self, current_price, take_profit, direction):
        """Detección robusta de take profit con tolerancia"""
        tolerance = TP_SL_TOLERANCE

        if direction == 1:  # LONG
            # Para LONG, TP se activa si precio >= take_profit (con tolerancia)
            return current_price >= (take_profit * (1 - tolerance))
        else:  # SHORT
            # Para SHORT, TP se activa si precio <= take_profit (con tolerancia)
            return current_price <= (take_profit * (1 + tolerance))

    def check_daily_loss_limit(self):
        """Verifica límite de pérdida diaria"""
        daily_loss_pct = abs(self.daily_pnl / self.initial_capital)

        if daily_loss_pct >= MAX_DAILY_LOSS:
            logger.log(f"🛑 Límite diario alcanzado: {daily_loss_pct:.2%}", "WARNING")
            return True
        return False

    def update_capital(self, pnl):
        """Actualiza capital y estadísticas"""
        self.current_capital += pnl
        self.daily_pnl += pnl

        # Log estado
        logger.log(f"💰 Capital: ${self.current_capital:.2f} | PnL Diario: ${self.daily_pnl:.2f}")

class SimulatedTrader:
    """Trading en modo simulación"""

    def __init__(self):
        self.data_manager = DataManager()
        self.feature_engineer = FeatureEngineer()
        self.signal_generator = RobustSignalGenerator()
        self.risk_manager = RiskManager()
        self.active_positions = []

    def train_system(self, df):
        """Entrena el sistema con datos históricos"""
        # Crear features
        df_features = self.feature_engineer.create_features(df.copy())

        # Preparar datos
        X, y = self.signal_generator.prepare_data(df_features)

        if X is not None and len(X) > 100:
            # Split 80/20
            split_idx = int(0.8 * len(X))
            X_train, y_train = X[:split_idx], y[:split_idx]

            # Escalar datos
            X_train_scaled = self.signal_generator.scaler.fit_transform(X_train)

            # Entrenar modelos
            self.signal_generator.train_models(X_train_scaled, y_train)

            logger.log("✅ Sistema entrenado y listo")
            logger.log(f"📊 Modelos activos: {list(self.signal_generator.models.keys())}")
            return True

        return False

    def execute_trade(self, signal, current_df, current_price=None):
        """Ejecuta trade en simulación con información completa"""
        # Calcular parámetros
        position_size = self.risk_manager.calculate_position_size(signal['confidence'])

        # Obtener ATR actual del DataFrame
        if 'atr' in current_df.columns and not current_df['atr'].empty:
            current_atr = current_df['atr'].iloc[-1]
            if pd.isna(current_atr) or current_atr <= 0:
                current_atr = current_df['close'].iloc[-1] * 0.005
        else:
            current_atr = current_df['close'].iloc[-1] * 0.005

        # Usar el precio actual real para la entrada
        entry_price = current_price if current_price is not None else current_df['close'].iloc[-1]

        stop_loss = self.risk_manager.calculate_stop_loss(
            entry_price, current_atr, signal['signal']
        )

        take_profit = self.risk_manager.calculate_take_profit(
            entry_price, current_atr, signal['signal']
        )

        trade = {
            'timestamp': signal['timestamp'],
            'type': 'LONG' if signal['signal'] == 1 else 'SHORT',
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'position_size': position_size,
            'confidence': signal['confidence'],
            'status': 'OPEN',
            'entry_time': datetime.datetime.now()
        }

        self.active_positions.append(trade)

        # Log detallado
        logger.log(f"\n{'='*60}")
        logger.log(f"🎯 NUEVA POSICIÓN ABIERTA:")
        logger.log(f"├─ Tipo: {'🟢 LONG' if trade['type'] == 'LONG' else '🔴 SHORT'}")
        logger.log(f"├─ Entrada: ${trade['entry_price']:.2f}")
        logger.log(f"├─ Stop Loss: ${stop_loss:.2f} ({abs(stop_loss-trade['entry_price'])/trade['entry_price']*100:.2f}%)")
        logger.log(f"├─ Take Profit: ${take_profit:.2f} ({abs(take_profit-trade['entry_price'])/trade['entry_price']*100:.2f}%)")
        logger.log(f"├─ Tamaño: ${position_size:.2f}")
        logger.log(f"├─ Confianza: {trade['confidence']:.2%}")
        logger.log(f"└─ Risk/Reward: 1:{abs(take_profit-trade['entry_price'])/abs(stop_loss-trade['entry_price']):.1f}")
        logger.log(f"{'='*60}\n")

        return trade

    def update_positions(self, current_price, current_atr=None):
        """Actualiza posiciones abiertas con detección robusta de TP/SL SIN trailing"""
        for position in self.active_positions:
            if position['status'] != 'OPEN':
                continue

            direction = 1 if position['type'] == 'LONG' else -1

            # Actualizar mejor precio para estadísticas (sin afectar TP/SL)
            if 'best_price' not in position:
                position['best_price'] = position['entry_price']

            # Actualizar mejor precio solo para tracking
            if position['type'] == 'LONG' and current_price > position['best_price']:
                position['best_price'] = current_price
            elif position['type'] == 'SHORT' and current_price < position['best_price']:
                position['best_price'] = current_price

            # NO TRAILING STOP/PROFIT - Mantener niveles originales
            # Los niveles de TP/SL se mantienen fijos desde la entrada

            # Calcular distancias para alertas
            if position['type'] == 'LONG':
                dist_to_sl = (current_price - position['stop_loss']) / position['stop_loss'] * 100
                dist_to_tp = (position['take_profit'] - current_price) / current_price * 100

                # Alertas de proximidad mejoradas
                if 0 < dist_to_sl < 1.0:  # Dentro del 1%
                    logger.log(f"🚨 ALERTA CRÍTICA: LONG muy cerca de SL! Distancia: {dist_to_sl:.3f}%")
                elif 0 < dist_to_tp < 1.0:  # Dentro del 1%
                    logger.log(f"🎯 ALERTA: LONG cerca de TP! Distancia: {dist_to_tp:.3f}%")

            else:  # SHORT
                dist_to_sl = (position['stop_loss'] - current_price) / current_price * 100
                dist_to_tp = (current_price - position['take_profit']) / position['take_profit'] * 100

                # Alertas de proximidad mejoradas
                if 0 < dist_to_sl < 1.0:  # Dentro del 1%
                    logger.log(f"🚨 ALERTA CRÍTICA: SHORT muy cerca de SL! Distancia: {dist_to_sl:.3f}%")
                elif 0 < dist_to_tp < 1.0:  # Dentro del 1%
                    logger.log(f"🎯 ALERTA: SHORT cerca de TP! Distancia: {dist_to_tp:.3f}%")

            # Usar detección robusta de TP/SL
            if self.risk_manager.is_stop_loss_hit(current_price, position['stop_loss'], direction):
                logger.log(f"🛑 STOP LOSS ACTIVADO: {position['type']} @ ${current_price:.2f}")
                self.close_position(position, current_price, 'STOP_LOSS')
            elif self.risk_manager.is_take_profit_hit(current_price, position['take_profit'], direction):
                logger.log(f"🎯 TAKE PROFIT ACTIVADO: {position['type']} @ ${current_price:.2f}")
                self.close_position(position, current_price, 'TAKE_PROFIT')

    def close_position(self, position, exit_price, reason):
        """Cierra posición y calcula PnL con detalles"""
        if position['type'] == 'LONG':
            pnl = (exit_price - position['entry_price']) / position['entry_price'] * position['position_size']
            pnl_pct = (exit_price - position['entry_price']) / position['entry_price'] * 100
        else:  # SHORT
            pnl = (position['entry_price'] - exit_price) / position['entry_price'] * position['position_size']
            pnl_pct = (position['entry_price'] - exit_price) / position['entry_price'] * 100

        position['exit_price'] = exit_price
        position['pnl'] = pnl
        position['pnl_pct'] = pnl_pct
        position['status'] = 'CLOSED'
        position['exit_reason'] = reason
        position['exit_time'] = datetime.datetime.now()
        position['duration'] = (position['exit_time'] - position['entry_time']).seconds // 60

        # Actualizar capital
        self.risk_manager.update_capital(pnl)

        # Log detallado del resultado
        emoji = "💰" if pnl > 0 else "📉"
        logger.log(f"\n{emoji} POSICIÓN CERRADA:")
        logger.log(f"├─ Tipo: {position['type']}")
        logger.log(f"├─ Entrada: ${position['entry_price']:.2f}")
        logger.log(f"├─ Salida: ${exit_price:.2f}")
        logger.log(f"├─ Razón: {reason}")
        logger.log(f"├─ PnL: ${pnl:.2f} ({pnl_pct:.2f}%)")
        logger.log(f"├─ Duración: {position['duration']} minutos")
        logger.log(f"└─ Capital actualizado: ${self.risk_manager.current_capital:.2f}")

        # Si es el primer trade cerrado, mostrar información adicional
        closed_count = len([p for p in self.active_positions if p['status'] == 'CLOSED'])
        if closed_count == 1:
            logger.log(f"\n🎉 ¡PRIMER TRADE COMPLETADO!")
            logger.log(f"💡 El sistema está funcionando correctamente.")
            logger.log(f"📊 Continúa monitoreando para ver más trades...")

    def generate_report(self):
        """Genera reporte de rendimiento"""
        closed_positions = [p for p in self.active_positions if p['status'] == 'CLOSED']

        # Calcular métricas básicas
        current_capital = self.risk_manager.current_capital
        initial_capital = self.risk_manager.initial_capital
        roi = (current_capital - initial_capital) / initial_capital * 100

        if not closed_positions:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'total_pnl': 0,
                'sharpe_ratio': 0,
                'current_capital': current_capital,
                'roi': roi
            }

        # Calcular métricas
        wins = [p for p in closed_positions if p['pnl'] > 0]
        losses = [p for p in closed_positions if p['pnl'] < 0]

        win_rate = len(wins) / len(closed_positions) * 100

        total_wins = sum(p['pnl'] for p in wins) if wins else 0
        total_losses = abs(sum(p['pnl'] for p in losses)) if losses else 1

        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')

        # Calcular Sharpe Ratio
        returns = [p['pnl'] / p['position_size'] for p in closed_positions]
        if len(returns) > 1:
            sharpe = np.mean(returns) / (np.std(returns) + 1e-8) * np.sqrt(252)
        else:
            sharpe = 0

        report = {
            'total_trades': len(closed_positions),
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_pnl': sum(p['pnl'] for p in closed_positions),
            'sharpe_ratio': sharpe,
            'current_capital': current_capital,
            'roi': roi
        }

        return report

class LiveTradingBot:
    """Bot principal para trading en vivo (simulado)"""

    def __init__(self):
        self.trader = SimulatedTrader()
        self.is_trained = False
        self.last_signal_time = None
        self.timeframes = ['15m', '1h']
        self.total_trades_executed = 0

    def show_market_summary(self, df):
        """Muestra resumen rápido del mercado"""
        current_price = df['close'].iloc[-1]
        price_change = (current_price - df['close'].iloc[-5]) / df['close'].iloc[-5] * 100
        rsi = talib.RSI(df['close'])[-1]
        volume_avg = df['volume'].rolling(20).mean().iloc[-1]
        volume_ratio = df['volume'].iloc[-1] / volume_avg

        # Obtener símbolo del activo
        asset_symbol = SYMBOL.split('/')[0]

        logger.log(f"\n💹 MERCADO {asset_symbol}: ${current_price:.2f} ({price_change:+.2f}%) | "
                  f"RSI: {rsi:.0f} {'🔴' if rsi > 70 else '🟢' if rsi < 30 else '⚪'} | "
                  f"Vol: {'🔥' if volume_ratio > 1.5 else '📊'} x{volume_ratio:.1f}")

    def initialize(self):
        """Inicializa el bot con datos históricos"""
        logger.log("🚀 Inicializando Trading Bot ULTRA MEJORADO v4.1")
        logger.log(f"💱 Activo seleccionado: {SYMBOL}")
        logger.log("📚 Descargando datos históricos para entrenamiento optimizado...")
        logger.log("⏰ Usando timeframe de 1 hora para mayor estabilidad...")

        # Obtener MUCHOS más datos históricos con timeframe más largo
        df = self.trader.data_manager.get_live_data(timeframe='1h', limit=5000)

        if df.empty:
            logger.log("⚠️ No se pudieron obtener datos de 1h, intentando con 15m...", "WARNING")
            df = self.trader.data_manager.get_live_data(timeframe='15m', limit=5000)

        if not df.empty:
            logger.log("🤖 Entrenando ensemble ultra mejorado con 10+ modelos ML...")
            logger.log("🔧 Aplicando mejoras v4.1: modelos optimizados, features avanzados, SIN trailing")
            self.is_trained = self.trader.train_system(df)

            if self.is_trained:
                logger.log("✅ Bot v4.1 ULTRA MEJORADO inicializado correctamente")
                logger.log(f"📊 Sistema listo con {len(self.trader.signal_generator.models)} modelos validados")
                logger.log("✨ NUEVAS CARACTERÍSTICAS v4.1:")
                logger.log("  • 🔄 Validación Walk-Forward temporal mejorada")
                logger.log("  • 🏗️ Stacking Ensemble avanzado con meta-learner")
                logger.log("  • 🎯 Feature engineering avanzado (25+ features)")
                logger.log("  • 📊 Criterios de validación más flexibles")
                logger.log("  • 🧹 Filtrado automático de outliers mejorado")
                logger.log("  • 📈 10+ modelos ML optimizados")
                logger.log("  • 💾 Persistencia de modelos entrenados")
                logger.log("  • 🚫 SIN trailing stop/profit - niveles fijos")
                self.show_next_predictions(df)
            else:
                logger.log("❌ Error en inicialización", "ERROR")
        else:
            logger.log("❌ No se pudieron obtener datos", "ERROR")

    def show_next_predictions(self, df):
        """Muestra próximas predicciones con niveles de TP/SL"""
        logger.log("\n" + "="*60)
        logger.log("🔮 PRÓXIMAS PREDICCIONES")
        logger.log("="*60)

        # Crear features
        df_features = self.trader.feature_engineer.create_features(df.copy())

        # Generar señales
        signals = self.trader.signal_generator.generate_signals(df_features)

        if not signals.empty:
            # Mostrar solo la señal actual más reciente
            logger.log("\n📊 SEÑALES DE TRADING:")
            signal = signals.iloc[-1]  # Solo la más reciente
            direction = "🟢 LONG" if signal['signal'] == 1 else "🔴 SHORT"

            # Calcular TP/SL usando los multiplicadores globales
            atr = signal['atr'] if signal['atr'] > 0 else signal['price'] * 0.005
            if signal['signal'] == 1:  # LONG
                stop_loss = signal['price'] - (atr * ATR_MULTIPLIER_SL)
                take_profit = signal['price'] + (atr * ATR_MULTIPLIER_TP)
            else:  # SHORT
                stop_loss = signal['price'] + (atr * ATR_MULTIPLIER_SL)
                take_profit = signal['price'] - (atr * ATR_MULTIPLIER_TP)

            logger.log(f"\n{direction} @ ${signal['price']:.2f}")
            logger.log(f"├─ Stop Loss: ${stop_loss:.2f} ({abs(stop_loss-signal['price'])/signal['price']*100:.2f}%)")
            logger.log(f"├─ Take Profit: ${take_profit:.2f} ({abs(take_profit-signal['price'])/signal['price']*100:.2f}%)")
            logger.log(f"├─ Confianza: {signal['confidence']:.2%}")
            logger.log(f"└─ Hora: {signal['timestamp']}")

            # Mostrar información adicional sobre la señal actual
            logger.log(f"\n📊 DETALLES DE LA SEÑAL ACTUAL:")
            logger.log(f"├─ Consenso entre modelos: {signal['consensus_score']:.1%}")
            logger.log(f"├─ Confianza del ensemble: {signal['ensemble_confidence']:.1%}")
            logger.log(f"├─ ATR utilizado: ${atr:.2f}")
            logger.log(f"└─ Risk/Reward ratio: 1:{abs(take_profit-signal['price'])/abs(stop_loss-signal['price']):.1f}")
        else:
            logger.log("❌ No hay señales de alta confianza en este momento")

        # Mostrar estado del mercado detallado
        current_price = df['close'].iloc[-1]
        rsi = talib.RSI(df['close'])[-1]
        sma_20 = df['close'].rolling(20).mean().iloc[-1]
        sma_50 = df['close'].rolling(50).mean().iloc[-1]
        volume_avg = df['volume'].rolling(20).mean().iloc[-1]
        volatility = df['close'].pct_change().rolling(20).std().iloc[-1] * 100

        # Nuevos indicadores
        adx = talib.ADX(df['high'], df['low'], df['close'])[-1]
        cci = talib.CCI(df['high'], df['low'], df['close'])[-1]
        mfi = talib.MFI(df['high'], df['low'], df['close'], df['volume'])[-1]

        trend = "ALCISTA 📈" if df['close'].iloc[-1] > df['close'].iloc[-20] else "BAJISTA 📉"
        momentum = "FUERTE" if abs(df['close'].iloc[-1] - df['close'].iloc[-5]) / df['close'].iloc[-5] > 0.01 else "DÉBIL"

        asset_symbol = SYMBOL.split('/')[0]

        logger.log(f"\n📊 ESTADO DEL MERCADO {asset_symbol}:")
        logger.log(f"├─ Precio actual: ${current_price:.2f}")
        logger.log(f"├─ RSI: {rsi:.2f} {'(Sobrecompra)' if rsi > 70 else '(Sobreventa)' if rsi < 30 else '(Neutral)'}")
        logger.log(f"├─ ADX: {adx:.2f} {'(Tendencia fuerte)' if adx > 25 else '(Tendencia débil)'}")
        logger.log(f"├─ CCI: {cci:.2f}")
        logger.log(f"├─ MFI: {mfi:.2f}")
        logger.log(f"├─ SMA 20: ${sma_20:.2f} {'↑' if current_price > sma_20 else '↓'}")
        logger.log(f"├─ SMA 50: ${sma_50:.2f} {'↑' if current_price > sma_50 else '↓'}")
        logger.log(f"├─ Tendencia: {trend} ({momentum})")
        logger.log(f"├─ Volatilidad: {volatility:.2f}% {'🔥 ALTA' if volatility > 2 else '✅ NORMAL'}")
        logger.log(f"└─ Volumen: {'📊 Alto' if df['volume'].iloc[-1] > volume_avg * 1.5 else '📉 Normal'}")

    def run_live_simulation(self):
        """Ejecuta bot en modo simulación continua"""
        if not self.is_trained:
            logger.log("❌ Bot no está entrenado", "ERROR")
            logger.log("💡 Por favor, seleccione opción 1 para inicializar el sistema primero")
            return

        asset_symbol = SYMBOL.split('/')[0]

        logger.log(f"\n🤖 INICIANDO TRADING v4.1 ULTRA MEJORADO - {asset_symbol}")
        logger.log(f"💰 Capital inicial: ${self.trader.risk_manager.initial_capital}")
        logger.log(f"📍 Posiciones máximas: {MAX_POSITIONS}")
        logger.log(f"⏰ Timeframe: 1 hora (mayor estabilidad)")
        logger.log("\n📌 CARACTERÍSTICAS v4.1 ULTRA MEJORADAS:")
        logger.log("📌 • 10+ modelos ML optimizados con validación temporal")
        logger.log("📌 • Feature engineering avanzado (25+ features)")
        logger.log("📌 • Stacking Ensemble avanzado con meta-learner")
        logger.log("📌 • Criterios de validación más flexibles")
        logger.log("📌 • Umbrales adaptativos dinámicos optimizados")
        logger.log("📌 • Filtrado automático de outliers mejorado")
        logger.log("📌 • SIN trailing stop/profit - niveles fijos")
        logger.log(f"📌 • Confianza mínima: {MIN_CONFIDENCE:.0%} | Consenso mínimo: {ENSEMBLE_MIN_AGREEMENT:.0%}")
        logger.log(f"📌 • Tolerancia TP/SL: {TP_SL_TOLERANCE:.2%} | Trailing: ❌ DESACTIVADO (niveles fijos)")
        logger.log(f"📌 • ATR Multipliers: SL={ATR_MULTIPLIER_SL}x | TP={ATR_MULTIPLIER_TP}x")
        logger.log("📌 • Filtrado MEJORADO: Criterios más permisivos para generar más señales\n")

        cycle = 0
        while True:
            try:
                cycle += 1
                logger.log(f"\n{'='*60}")
                logger.log(f"📍 Ciclo #{cycle} - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                # Verificar límite diario
                if self.trader.risk_manager.check_daily_loss_limit():
                    logger.log("🛑 Límite diario alcanzado - Trading pausado")
                    time.sleep(3600)
                    continue

                # Obtener datos actuales con timeframe de 1 hora
                df = self.trader.data_manager.get_live_data(timeframe='1h', limit=500)

                if df.empty:
                    logger.log("⚠️ No se pudieron obtener datos de 1h, usando 15m...", "WARNING")
                    df = self.trader.data_manager.get_live_data(timeframe='15m', limit=500)

                if df.empty:
                    logger.log("⚠️ No hay datos disponibles", "WARNING")
                    time.sleep(60)
                    continue

                # Precio actual
                current_price = df['close'].iloc[-1]

                # Mostrar resumen de mercado
                if cycle <= 10 or cycle % 3 == 0:
                    self.show_market_summary(df)

                # Mostrar información del ciclo
                if cycle == 1:
                    logger.log(f"├─ Sistema: ✅ ACTIVO")
                    if len(self.trader.signal_generator.models) > 0:
                        logger.log(f"├─ Modelos ML: {len(self.trader.signal_generator.models)} activos")
                    else:
                        logger.log("├─ Modelos ML: 0 activos (rechazados por overfitting)")
                        logger.log("├─ Modo: 📊 Análisis técnico puro")
                    logger.log(f"├─ Confianza mínima: {MIN_CONFIDENCE:.0%}")
                logger.log(f"├─ Precio {asset_symbol}: ${current_price:.2f}")
                logger.log(f"├─ Posiciones abiertas: {len([p for p in self.trader.active_positions if p['status'] == 'OPEN'])}/{MAX_POSITIONS}")
                logger.log(f"├─ Trades ejecutados: {self.total_trades_executed}")
                logger.log(f"└─ Capital: ${self.trader.risk_manager.current_capital:.2f}")

                # Obtener ATR actual para trailing stop
                current_atr = None
                if 'atr' in df.columns and not df['atr'].empty:
                    current_atr = df['atr'].iloc[-1]
                    if pd.isna(current_atr) or current_atr <= 0:
                        current_atr = df['close'].iloc[-1] * 0.005
                else:
                    current_atr = df['close'].iloc[-1] * 0.005

                # Actualizar posiciones con precio y ATR actual
                self.trader.update_positions(current_price, current_atr)

                # Generar nuevas señales desde ciclo 2
                if cycle >= 2:
                    logger.log(f"\n🔍 [Ciclo {cycle}] Analizando mercado para oportunidades...")
                    df_features = self.trader.feature_engineer.create_features(df.copy())

                    # Mostrar algunos indicadores clave
                    try:
                        current_rsi = talib.RSI(df['close'])[-1]
                        current_macd = talib.MACD(df['close'])[0][-1]
                        current_adx = talib.ADX(df['high'], df['low'], df['close'])[-1]
                        logger.log(f"📊 Indicadores: RSI={current_rsi:.1f}, MACD={current_macd:.1f}, ADX={current_adx:.1f}")
                    except:
                        pass

                    signals = self.trader.signal_generator.generate_signals(df_features)

                    if not signals.empty:
                        logger.log(f"\n🎯 ¡{len(signals)} SEÑALES DETECTADAS!")

                        # Contar tipos de señales
                        long_signals = len(signals[signals['signal'] == 1])
                        short_signals = len(signals[signals['signal'] == -1])

                        if long_signals > 0:
                            logger.log(f"🟢 LONG: {long_signals} señales")
                        if short_signals > 0:
                            logger.log(f"🔴 SHORT: {short_signals} señales")

                        # Mostrar mejor señal
                        best_signal = signals.loc[signals['confidence'].idxmax()]
                        logger.log(f"🏆 Mejor señal: {'LONG' if best_signal['signal'] == 1 else 'SHORT'} con {best_signal['confidence']:.1%} confianza")

                        # Verificar posiciones abiertas
                        open_positions = [p for p in self.trader.active_positions if p['status'] == 'OPEN']
                        available_slots = MAX_POSITIONS - len(open_positions)

                        # Mostrar posiciones abiertas actual
                        logger.log(f"📊 Posiciones abiertas actuales: {len(open_positions)}")

                        if available_slots > 0:
                            logger.log(f"💼 Espacios disponibles para trades: {available_slots}")

                            # Tomar las mejores señales por confianza
                            signals_sorted = signals.sort_values('confidence', ascending=False)
                            signals_to_process = signals_sorted.head(available_slots)

                            for idx, (_, signal) in enumerate(signals_to_process.iterrows()):
                                logger.log(f"\n🔄 Procesando señal {idx+1}/{len(signals_to_process)}:")
                                logger.log(f"   Tipo: {'🟢 LONG' if signal['signal'] == 1 else '🔴 SHORT'}")
                                logger.log(f"   Precio: ${signal['price']:.2f}")
                                logger.log(f"   Confianza: {signal['confidence']:.2%}")

                                # Ejecutar directamente
                                logger.log("   ✅ EJECUTANDO TRADE")
                                try:
                                    self.trader.execute_trade(signal, df, current_price)
                                    self.total_trades_executed += 1
                                    logger.log("   ✅ Trade ejecutado exitosamente")
                                except Exception as e:
                                    logger.log(f"   ❌ Error ejecutando trade: {str(e)}", "ERROR")

                                # Pequeña pausa para ver la acción
                                time.sleep(1)
                        else:
                            logger.log(f"⚠️ Máximo de posiciones alcanzado ({MAX_POSITIONS}) - esperando cierre de posiciones")
                    else:
                        logger.log(f"❌ No hay señales con confianza suficiente (>{MIN_CONFIDENCE:.0%})")
                        # Mostrar info de debug
                        X, _ = self.trader.signal_generator.prepare_data(df_features)
                        if X is not None and len(X) > 0:
                            logger.log(f"🔍 Sistema analizando {len(X)} patrones de mercado...")

                # Mostrar estado de posiciones abiertas
                open_positions = [p for p in self.trader.active_positions if p['status'] == 'OPEN']
                if open_positions and cycle % 3 == 0:
                    logger.log("\n📋 POSICIONES ABIERTAS:")
                    for i, pos in enumerate(open_positions):
                        pnl_actual = ((current_price - pos['entry_price']) / pos['entry_price'] * 100
                                     if pos['type'] == 'LONG'
                                     else (pos['entry_price'] - current_price) / pos['entry_price'] * 100)

                        # Emoji según PnL
                        pnl_emoji = "🟢" if pnl_actual > 0 else "🔴" if pnl_actual < 0 else "⚪"

                        logger.log(f"{i+1}. {pos['type']} @ ${pos['entry_price']:.2f} | "
                                  f"PnL: {pnl_emoji} {pnl_actual:+.2f}% | "
                                  f"Tiempo: {(datetime.datetime.now() - pos['entry_time']).seconds // 60} min")

                # Mostrar estado cada 5 ciclos
                if cycle % 5 == 0:
                    report = self.trader.generate_report()
                    logger.log(f"\n📊 REPORTE DE RENDIMIENTO:")
                    logger.log(f"├─ Trades totales: {report['total_trades']}")
                    logger.log(f"├─ Win Rate: {report['win_rate']:.1f}%")
                    logger.log(f"├─ Profit Factor: {report['profit_factor']:.2f}")
                    logger.log(f"├─ PnL Total: ${report['total_pnl']:.2f}")
                    logger.log(f"├─ ROI: {report.get('roi', 0):.2f}%")
                    logger.log(f"└─ Capital actual: ${report['current_capital']:.2f}")

                    # Solo mostrar predicciones cada 10 ciclos
                    if cycle % 10 == 0:
                        self.show_next_predictions(df)

                # Esperar antes del próximo ciclo (timeframe 1h = más tiempo)
                time.sleep(120)  # 2 minutos para timeframe de 1 hora

            except KeyboardInterrupt:
                logger.log("\n🛑 Trading detenido por el usuario")
                break
            except Exception as e:
                logger.log(f"❌ Error en ciclo: {str(e)}", "ERROR")
                time.sleep(60)

        # Reporte final
        logger.log("\n" + "="*60)
        logger.log("📊 REPORTE FINAL")
        logger.log("="*60)

        final_report = self.trader.generate_report()
        for key, value in final_report.items():
            if isinstance(value, float):
                logger.log(f"{key}: {value:.2f}")
            else:
                logger.log(f"{key}: {value}")

def main():
    """Función principal"""
    asset_symbol = SYMBOL.split('/')[0]

    print("\n" + "="*70)
    print(f"🚀 {asset_symbol} TRADING BOT ANTI-OVERFITTING v4.2")
    print("\n⚡ IMPORTANTE:")
    print("1. Primero DEBES inicializar el sistema (opción 1)")
    print("2. El bot usa ML optimizado con 10+ modelos avanzados")
    print("3. Sistema SIN trailing stop/profit - niveles fijos para máxima precisión")
    print("4. Los resultados son 100% simulados - NO es dinero real")
    print("="*70)
    print("�️ NUEVAS MEJORAS ANTI-OVERFITTING v4.2:")
    print("  • 🤖 10+ modelos ML ultra simples (RF, XGB, LightGBM, CatBoost, MLP, SVM, etc.)")
    print("  • 🔄 Validación Walk-Forward temporal robusta")
    print("  • 🏗️ Stacking Ensemble con regularización extrema")
    print("  • 🎯 Feature selection ultra restrictivo (solo 10 features)")
    print("  • 📊 Regularización extrema en todos los modelos")
    print("  • 🧹 Filtrado automático de outliers")
    print("  • 📈 Criterios de validación anti-overfitting")
    print("  • 💾 Persistencia de modelos entrenados")
    print("  • 🔍 Validación cruzada temporal de 7 folds")
    print("  • ⚖️ Balance automático de clases")
    print("  • 🎲 Regularización L1, L2 y ElasticNet extrema")
    print("  • 🚫 SIN trailing stop/profit - niveles fijos desde entrada")
    print("  • 📊 ATR multipliers optimizados: SL=2.5x, TP=4.0x")
    print("  • 🛡️ Modelos ultra simples para evitar overfitting")
    print("="*70)

    bot = LiveTradingBot()

    # Menú principal
    while True:
        print("\nSeleccione opción:")
        print("1. Inicializar/Reinicializar sistema")
        print("2. Ejecutar simulación en vivo ⭐ (RECOMENDADO)")
        print("3. Ver predicciones actuales")
        print("4. Salir")

        opcion = input("\nOpción (1-4): ")

        if opcion == '1':
            print("\n🔄 Inicializando sistema...")
            print("⏳ Esto puede tomar 30-60 segundos...")
            bot.initialize()
            print("\n✅ Sistema listo para trading!")
            print("💡 Ahora seleccione opción 2 para comenzar la simulación")
        elif opcion == '2':
            if bot.is_trained:
                bot.run_live_simulation()
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
                print("💡 El sistema necesita ser entrenado antes de ejecutar la simulación")
        elif opcion == '3':
            if bot.is_trained:
                df = bot.trader.data_manager.get_live_data(timeframe='1h', limit=500)
                if df.empty:
                    df = bot.trader.data_manager.get_live_data(timeframe='15m', limit=500)
                bot.show_next_predictions(df)
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
        elif opcion == '4':
            print(f"👋 Gracias por usar {asset_symbol} Trading Bot ULTRA MEJORADO v4.1")
            print("🚀 Sistema optimizado para máxima performance sin trailing stop/profit")
            break
        else:
            print("❌ Opción inválida")

if __name__ == "__main__":
    main()
